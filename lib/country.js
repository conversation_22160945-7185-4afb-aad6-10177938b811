const lookup = require('country-code-lookup');
const locationLib = require('../lib/location');

const group1Names = [
  'Philippines', 'Bolivia', 'Ukraine', 'Morocco', 'Egypt', 'Laos',
  'Vietnam', 'India', 'Comoros', 'Botswana', 'Armenia', 'Georgia',
  'Democratic Republic of the Congo', 'Senegal', 'Sri Lanka', 'Nigeria',
  'Bangladesh', 'Kenya', 'Pakistan', 'Cameroon', 'Cambodia',
  'Ethiopia', 'Sudan', 'Afghanistan', 'Liberia', 'Indonesia',
  'Sierra Leone', 'Togo', 'Haiti', 'Burundi', 'Benin', 'Rwanda',
  'Chad', 'Zambia', 'Mali', 'Burkina Faso', 'Niger', 'Madagascar',
  'Nepal', 'Yemen', 'Ghana', 'Angola', 'Uganda', 'Tanzania',
]
const group1 = group1Names.map((x) => lookup.byCountry(x).iso2);

function getCountryGroup(countryCode) {
  if (!countryCode) {
    return undefined;
  }
  if (group1.includes(countryCode)) {
    return 1;
  }
  return 0;
}

const gdprCountries = [
  'Austria',
  'Belgium',
  'Bulgaria',
  'Croatia',
  'Cyprus',
  'Czech Republic',
  'Denmark',
  'Estonia',
  'Finland',
  'France',
  'Germany',
  'Greece',
  'Hungary',
  'Ireland',
  'Italy',
  'Latvia',
  'Lithuania',
  'Luxembourg',
  'Malta',
  'Netherlands',
  'Poland',
  'Portugal',
  'Romania',
  'Slovakia',
  'Slovenia',
  'Spain',
  'Sweden',
  'Iceland',
  'Liechtenstein',
  'Norway',
];

/*
const gdprCountryCodes = gdprCountries.map(x => locationLib.getCountryCode(x));
console.log(gdprCountryCodes);
*/

function shouldRemoveCountryFilter(user) {
  const country = ['India', 'Philippines'].find(x => [user.actualCountry, user.ipData?.country, locationLib.getCountryNameFromTimezone(user.timezone)].includes(x));
  if (country == 'India') {
    return 'IN';
  }
  if (country == 'Philippines') {
    return 'PH';
  }
  return;
}

const CONTINENTS = {
  Africa: [
      'Algeria',
      'Angola',
      'Benin',
      'Botswana',
      'Burkina Faso',
      'Burundi',
      'Cameroon',
      'Cape Verde',
      'Central African Republic',
      'Chad',
      'Comoros',
      'Congo (Republic and DRC)',
      "Côte d'Ivoire",
      'Djibouti',
      'Egypt',
      'Equatorial Guinea',
      'Eritrea',
      'Eswatini',
      'Ethiopia',
      'Gabon',
      'Gambia',
      'Ghana',
      'Guinea',
      'Guinea-Bissau',
      'Kenya',
      'Lesotho',
      'Liberia',
      'Libya',
      'Madagascar',
      'Malawi',
      'Mali',
      'Mauritania',
      'Mauritius',
      'Morocco',
      'Mozambique',
      'Namibia',
      'Niger',
      'Nigeria',
      'Rwanda',
      'Sao Tome and Principe',
      'Senegal',
      'Seychelles',
      'Sierra Leone',
      'Somalia',
      'South Africa',
      'South Sudan',
      'Sudan',
      'Tanzania',
      'Togo',
      'Tunisia',
      'Uganda',
      'Western Sahara',
      'Zambia',
      'Zimbabwe'
  ],
  Asia: [
      'Afghanistan', 'Armenia', 'Azerbaijan',
      'Bahrain', 'Bangladesh', 'Bhutan',
      'Brunei', 'Cambodia', 'China',
      'Georgia', 'Hong Kong', 'India',
      'Indonesia', 'Iran', 'Iraq',
      'Israel', 'Japan', 'Jordan',
      'Kazakhstan', 'Kuwait', 'Kyrgyzstan',
      'Laos', 'Lebanon', 'Malaysia',
      'Maldives', 'Mongolia', 'Myanmar',
      'Nepal', 'North Korea', 'Oman',
      'Pakistan', 'Palestine', 'Philippines',
      'Qatar', 'Saudi Arabia', 'Singapore',
      'South Korea', 'Sri Lanka', 'Syria',
      'Taiwan', 'Tajikistan', 'Thailand',
      'Timor-Leste', 'Turkmenistan', 'United Arab Emirates',
      'Uzbekistan', 'Vietnam', 'Yemen'
  ],
  Europe: [
      'Albania',
      'Andorra',
      'Austria',
      'Belarus',
      'Belgium',
      'Bosnia and Herzegovina',
      'Bulgaria',
      'Croatia',
      'Cyprus',
      'Czechia',
      'Denmark',
      'Estonia',
      'Finland',
      'France',
      'Germany',
      'Greece',
      'Hungary',
      'Iceland',
      'Ireland',
      'Italy',
      'Kosovo',
      'Latvia',
      'Liechtenstein',
      'Lithuania',
      'Luxembourg',
      'Malta',
      'Moldova',
      'Monaco',
      'Montenegro',
      'Netherlands',
      'North Macedonia',
      'Norway',
      'Poland',
      'Portugal',
      'Romania',
      'Russia',
      'San Marino',
      'Serbia',
      'Slovakia',
      'Slovenia',
      'Spain',
      'Sweden',
      'Switzerland',
      'Turkey',
      'UK',
      'Ukraine',
      'Vatican City',
      'Yugoslavia'
  ],
  'North America': [
      'Canada',
      'US',
      'Antigua and Barbuda',
      'Bahamas',
      'Barbados',
      'Belize',
      'Bermuda',
      'Cayman Islands',
      'Costa Rica',
      'Cuba',
      'Dominica',
      'Dominican Republic',
      'El Salvador',
      'French Polynesia',
      'Grenada',
      'Guatemala',
      'Haiti',
      'Honduras',
      'Jamaica',
      'Mexico',
      'Montserrat',
      'Netherlands Antilles',
      'Nicaragua',
      'Panama',
      'Saint Kitts and Nevis',
      'Saint Lucia',
      'Saint Vincent and the Grenadines',
      'Trinidad and Tobago'
  ],
  Oceania: [
      'Australia', 'Fiji',
      'Kiribati', 'Marshall Islands',
      'Micronesia', 'Nauru',
      'New Zealand', 'Palau',
      'Papua New Guinea', 'Samoa',
      'Solomon Islands', 'Tonga',
      'Tuvalu', 'Vanuatu'
  ],
  'South America': [
      'Argentina', 'Aruba',
      'Bolivia', 'Brazil',
      'Chile', 'Colombia',
      'Curaçao', 'Ecuador',
      'Guyana', 'Paraguay',
      'Peru', 'Suriname',
      'Uruguay', 'Venezuela'
  ]
}

module.exports = {
  getCountryGroup,
  group1,
  group1Names,
  gdprCountries,
  shouldRemoveCountryFilter,
  CONTINENTS,
};
