/* eslint-disable no-unused-vars */
const superagent = require('superagent');
const fs = require('fs');
const axios = require('axios');
const { parse } = require('csv-parse/sync');
const { MongoClient } = require('mongodb');
const socialLib = require('../../lib/social');
const databaseLib = require('../../lib/database');
const blogsLib = require('../../lib/blogs');
const sitemapLib = require('../../lib/sitemap');
const { validMbti } = require('../../lib/personality');
const { s3 } = require('../../lib/s3');
const { sendEmailForNewSitemapIndexSES } = require('../../lib/email');
const { getSitemapExperimentUrl, getSitemapAutoIndexUrl } = require('../../lib/experiment-sitemap');

const chunkSize = 20000;
const WEB_DOMAIN = process.env.WEB_DOMAIN || 'https://boo.world';
const AUTO_INDEX_API_KEY = process.env.AUTO_INDEX_API_KEY || 'ed5e1668f023405bae547a4d3474fc30';
const AUTO_INDEX_SEARCH_ENGINE_ENDPOINT = process.env.AUTO_INDEX_SEARCH_ENGINE_ENDPOINT || 'https://api.indexnow.org/indexnow';

const locales = ['en', 'af', 'sq', 'ar', 'hy', 'az', 'bn', 'bg', 'ca', 'zh-Hans', 'zh-Hant', 'cs', 'da', 'nl', 'et', 'fil', 'fi', 'fr', 'gl', 'ka', 'de', 'el', 'he', 'hi', 'hu', 'id', 'it', 'ja', 'kk', 'ko', 'lt', 'ms', 'ml', 'mr', 'no', 'or', 'fa', 'pl', 'pt', 'pa', 'ro', 'ru', 'sr', 'si', 'sk', 'sl', 'es', 'sw', 'sv', 'te', 'th', 'tr', 'uk', 'ur', 'vi'];

async function backfillInterestPoints(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await socialLib.backfillInterestPoints();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function updateQuestionScores(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await socialLib.updateQuestionScores();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function updateCommentScores(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await socialLib.updateCommentScores();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

function getLocalizedUrl(url, lang) {
  if (!lang || lang == 'en') {
    return url;
  }
  const path = url.replace(WEB_DOMAIN, '');
  return `${WEB_DOMAIN}/${lang}${path}`;
}


function generateLocalizedLinks(url) {
  let links = [];
  for (const lang of locales) {
    if (lang) {
      links.push(getLocalizedUrl(url, lang));
    }
  }
  return links;
}

function generateXmlSitemapBody(urls, withImages, withFullImageUrl) {
  let body = '<?xml version="1.0" encoding="UTF-8"?>\n'
           + '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n'
           + '        xmlns:xhtml="http://www.w3.org/1999/xhtml"\n'
           + '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">\n';

  if (withImages || withFullImageUrl) {
    for (const url of urls) {
      body += '  <url>\n';
      body += `    <loc>${url.url}</loc>\n`;
      if (url.lastModified && url.lastModified instanceof Date) {
        body += `    <lastmod>${url.lastModified.toISOString().substr(0, 10)}</lastmod>\n`;
      }
      if (url.priority) {
        body += `    <priority>${url.priority}</priority>\n`;
      }
      if (url.changefreq) {
        body += `    <changefreq>${url.changefreq}</changefreq>\n`;
      }
      if (url.image) {
        body += '    <image:image>\n';
        if (withImages) {
          body += `      <image:loc>https://images.prod.boo.dating/${url.image}</image:loc>\n`;
        } else {
          body += `      <image:loc>${url.image}</image:loc>\n`;
        }
        body += '    </image:image>\n';
      }
      body += '  </url>\n';
    }
  } else {
    for (const url of urls) {
      body += '  <url>\n';
      body += `    <loc>${url}</loc>\n`;
      body += '  </url>\n';
    }
  }

  body += '</urlset>';
  return body;
}

async function uploadToS3(key, body) {
  try {
    console.log(`createSitemap - uploadToS3 function: sitemap creation initiated for key ${key}`)
    const params = {
      Bucket: process.env.AWS_SOULVERSE_S3_BUCKET || 'MOCK_SOULVERSE_S3_BUCKET',
      Key: key,
      Body: body,
    };
    const response = await s3.putObject(params).promise();
    console.log(`createSitemap - uploadToS3 function: sitemap creation and upload to S3 SUCCESS for key:`, key, response);
  } catch(err) {
    console.log(`createSitemap - uploadToS3 function error handler: error for ${key} and error is ==`,err)
  }
}

async function checkIfFileExistsInS3(key) {
  try {
    const params = {
      Bucket: process.env.AWS_SOULVERSE_S3_BUCKET || 'MOCK_SOULVERSE_S3_BUCKET',
      Key: key,
    };
    await s3.headObject(params).promise();
    console.log(`File exists in S3 for key: ${key}`);
    return true;
  } catch (err) {
    if (err.code === 'NotFound') {
      console.log(`File does not exist in S3 for key: ${key}`);
      return false;
    }
    console.error(`Error checking if file exists in S3 for key: ${key}`, err);
    throw err;
  }
}

async function createAndUploadSitemaps(urls, prefix, withImages, withFullImageUrl) {
  try {
    console.log(`createSitemap - createAndUploadSitemaps function: ${prefix} with total urls ${urls.length}`)
    const sitemaps = [];
    for (let i = 0, j = urls.length; i < j; i += chunkSize) {
    const chunk = urls.slice(i, i + chunkSize);
    const body = generateXmlSitemapBody(chunk, withImages, withFullImageUrl);
    const sitemap = `sitemaps/sitemap_${prefix}_${i}.xml`;
    sitemaps.push(sitemap);

    await uploadToS3(sitemap, body);
  }

  return sitemaps;
} catch(err) {
  console.log(`createSitemap - createAndUploadSitemaps function error handler: error for ${prefix} and error is ==`,err)
}
}

async function createAndUploadTranslatedSitemaps(urls, prefix, withImages) {

  const sitemaps = [];
  for (const lang of locales) {
    for (let i = 0, j = urls.length; i < j; i += chunkSize) {
      let chunk = urls.slice(i, i + chunkSize);
      if (withImages) {
        chunk = chunk.map(function(x) {
          return {
            url: getLocalizedUrl(x.url, lang),
            image: x.image,
          };
        });
      } else {
        chunk = chunk.map(x => getLocalizedUrl(x, lang));
      }
      const body = generateXmlSitemapBody(chunk, withImages);
      const sitemap = `sitemaps/sitemap_${lang}_${prefix}_${i}.xml`;
      sitemaps.push(sitemap);

      await uploadToS3(sitemap, body);
    }
  }

  return sitemaps;
}

async function createAndUploadTranslatedSitemapsV2(urlsLanguage, prefix, withImages) {

  const sitemaps = [];
  for (const lang in urlsLanguage) {
    const urls=[...urlsLanguage[lang]]
    for (let i = 0, j = urls.length; i < j; i += chunkSize) {
      let chunk = urls.slice(i, i + chunkSize);
      if (withImages) {
        chunk = chunk.map(function(x) {
          return {
            url: getLocalizedUrl(x.url, lang),
            image: x.image,
            lastModified: x.lastModified,
          };
        });
      } else {
        chunk = chunk.map(x => getLocalizedUrl(x, lang));
      }
      const body = generateXmlSitemapBody(chunk, withImages);
      const sitemap = `sitemaps/sitemap_${lang}_${prefix}_${i}.xml`;
      sitemaps.push(sitemap);
      await uploadToS3(sitemap, body);
    }
  }

  return sitemaps;
}

async function createSitemapIndex(categorySitemaps, sitemapIndexName) {
  const MAX_ENTRIES = 1000;
  const sitemapIndexNames = [];
  const newSitemapIndex = []
  for (const [category, sitemaps] of Object.entries(categorySitemaps)) {
    if (!sitemaps || sitemaps.length === 0) continue;
    let partNumber = 1;
    for (let i = 0; i < sitemaps.length; i += MAX_ENTRIES) {
      const chunk = sitemaps.slice(i, i + MAX_ENTRIES);
      let fileName = sitemapIndexName[category]
      if (partNumber > 1) {
        fileName = `${sitemapIndexName[category].replace('.xml', '')}_${partNumber}.xml`;
      }
      sitemapIndexNames.push(fileName);
      let body = '<?xml version="1.0" encoding="UTF-8"?>\n'
               + '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
      for (const sitemap of chunk) {
        body += '  <sitemap>\n';
        body += `    <loc>${WEB_DOMAIN}/${sitemap}</loc>\n`;
        body += '  </sitemap>\n';
      }
      body += '</sitemapindex>';
      const fileExists = await checkIfFileExistsInS3(fileName);
      if (!fileExists) {
        newSitemapIndex.push(`${WEB_DOMAIN}/${fileName}`);
      }
      await uploadToS3(fileName, body);

      partNumber++;
    }
  }
  if (newSitemapIndex && newSitemapIndex.length) {
    await sendEmailForNewSitemapIndexSES(newSitemapIndex)
  }
  return sitemapIndexNames;
}

async function createRobotsTxt(sitemapIndexCreated) {
  let body = `
User-Agent: *
Crawl-delay: 1
Disallow:/*?after=*
Disallow:/*&after=*
Disallow:/*?before=*
Disallow:/*&before=*
Disallow:/*?sort=*
Disallow:/*&sort=*
Disallow:/*?telepathy=*
Disallow:/*&telepathy=*
Disallow:/*?postId=*
Disallow:/*&postId=*
Disallow:/*?commentId=*
Disallow:/*&commentId=*
Disallow:/*?parentId=*
Disallow:/*&parentId=*
# Below are from Squarespace
Disallow: /config
Disallow: /search
Disallow: /account$
Disallow: /account/
Disallow: /commerce/digital-download/
Disallow: /api/
Allow: /api/ui-extensions/
Disallow:/*?author=*
Disallow:/*&author=*
Disallow:/*?tag=*
Disallow:/*&tag=*
Disallow:/*?month=*
Disallow:/*&month=*
Disallow:/*?view=*
Disallow:/*&view=*
Disallow:/*?format=json
Disallow:/*&format=json
Disallow:/*?format=page-context
Disallow:/*&format=page-context
Disallow:/*?format=main-content
Disallow:/*&format=main-content
Disallow:/*?format=json-pretty
Disallow:/*&format=json-pretty
Disallow:/*?format=ical
Disallow:/*&format=ical
Disallow:/*?reversePaginate=*
Disallow:/*&reversePaginate=*

`;
  for (const sitemapPath of sitemapIndexCreated) {
    body += `Sitemap: ${WEB_DOMAIN}/${sitemapPath}\n`;
  }
  await uploadToS3('robots.txt', body);
}

async function removeUnusedSitemaps(currentSitemaps) {
  const sitemapsToKeep = new Set(currentSitemaps)

  const params = {
    Bucket: process.env.AWS_SOULVERSE_S3_BUCKET || 'MOCK_SOULVERSE_S3_BUCKET',
    Prefix: 'sitemaps/',
  };

  let isTruncated = true;
  while (isTruncated) {
    const response = await s3.listObjectsV2(params).promise();
    console.log(params, response)
    const objectsToDelete = response.Contents
      .filter(item => !sitemapsToKeep.has(item.Key))
      .map(item => ({ Key: item.Key }));
    console.log(`sitemaps to delete: ${objectsToDelete.map(x => x.Key)}`);
    if (objectsToDelete.length > 0) {
      const deleteParams = {
        Bucket: process.env.AWS_SOULVERSE_S3_BUCKET || 'MOCK_SOULVERSE_S3_BUCKET',
        Delete: {
          Objects: objectsToDelete,
        },
      };
      await s3.deleteObjects(deleteParams).promise();
    }
    isTruncated = response.IsTruncated;
    if (isTruncated) {
      params.ContinuationToken = response.NextContinuationToken;
    }
  }
}

async function updateSitemap(req, res, next) {
  try{
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }
  let allCategorySitemaps = {
    allResourceSitemaps: [],
    allPillarsSitemaps: [],
    allDatabaseCategoriesSitemaps: [],
    allDatabaseProfilesSitemaps: [],
    allUniverseSitemaps: [],
    allForumPostsSitemaps: [],
    allOtherSitemaps: [
      'sitemaps/sitemap_meme_kit_0.xml',
      'sitemaps/sitemap_assertive_turbulent_resources.xml',
      'sitemaps/sitemap_boo_pages.xml',
    ],
  }

  let sitemapIndexName = {
    allResourceSitemaps: 'sitemaps/index_sitemap_resources.xml',
    allPillarsSitemaps: 'sitemaps/index_sitemap_pillars.xml',
    allDatabaseCategoriesSitemaps: 'sitemaps/index_sitemap_database.xml',
    allDatabaseProfilesSitemaps: 'sitemaps/index_sitemap_database_profiles.xml',
    allUniverseSitemaps: 'sitemaps/index_sitemap_universe.xml',
    allForumPostsSitemaps: 'sitemaps/index_sitemap_universe_posts.xml',
    allOtherSitemaps: 'sitemaps/index_sitemap_other.xml',
  }

    //create experimental sitemaps
  {
    const urls = await getSitemapExperimentUrl()
    console.log(`createSitemap - Url return success from createSitemap for sitemap experiments :${urls.length}`);
    const sitemaps = await createAndUploadSitemaps(urls, 'experiments', true);
    allCategorySitemaps.allOtherSitemaps = allCategorySitemaps.allOtherSitemaps.concat(sitemaps);
  }

  // create home sitemaps
  {
    const personalityTest = `${WEB_DOMAIN}/16-personality-test`;
    const urls = generateLocalizedLinks(WEB_DOMAIN)
                 .concat(generateLocalizedLinks(personalityTest))
    const sitemaps = await createAndUploadSitemaps(urls, 'home');
    allCategorySitemaps.allOtherSitemaps = allCategorySitemaps.allOtherSitemaps.concat(sitemaps);
  }

  // create forum sitemaps
  {
    const urls = await socialLib.createSitemap();
    console.log(`createSitemap - Url return success from createSitemap for posts and count is :${urls.length}`);
    const sitemaps = await createAndUploadSitemaps(urls, 'forum_posts', true);
    allCategorySitemaps.allForumPostsSitemaps = allCategorySitemaps.allForumPostsSitemaps.concat(sitemaps);
  }

  {
    const urls = await socialLib.createInterestSitemap();
    const sitemaps = await createAndUploadSitemaps(urls, 'forum_interests', true);
    allCategorySitemaps.allUniverseSitemaps = allCategorySitemaps.allUniverseSitemaps.concat(sitemaps);
  }

  // html sitemaps
  {
    const urls = await sitemapLib.createHtmlSitemap();
    const sitemaps = await createAndUploadSitemaps(urls, 'html_sitemaps');
    allCategorySitemaps.allOtherSitemaps = allCategorySitemaps.allOtherSitemaps.concat(sitemaps);
  }

  /*
  {
    const pathToCsv = __dirname + '/../../lib/explore-keywords.csv';
    const input = fs.readFileSync(pathToCsv);
    const records = parse(input);
    const urls = records.map((x) => `${WEB_DOMAIN}/explore/${encodeURIComponent(x[0])}`);
    const sitemaps = await createAndUploadSitemaps(urls, 'forum_explore');
    allCategorySitemaps = allCategorySitemaps.concat(sitemaps);
  }
  */

  // create database sitemaps
  const { homeUrls, categoryUrls, subcategoryUrls, profileUrls } = await databaseLib.createDatabaseSitemapsV2();
  const { resourceUrls, pillarUrls } = await blogsLib.createBlogsSitemaps();

  {
    const sitemaps = await createAndUploadTranslatedSitemaps(homeUrls, 'database_home');
    allCategorySitemaps.allDatabaseCategoriesSitemaps = allCategorySitemaps.allDatabaseCategoriesSitemaps.concat(sitemaps);
  }

  {
    const sitemaps = await createAndUploadSitemaps(resourceUrls, 'resources', false, true);
    allCategorySitemaps.allResourceSitemaps = allCategorySitemaps.allResourceSitemaps.concat(sitemaps);
  }

  {
    const sitemaps = await createAndUploadSitemaps(pillarUrls, 'pillars', false, true);
    allCategorySitemaps.allPillarsSitemaps = allCategorySitemaps.allPillarsSitemaps.concat(sitemaps);
  }

  {
    const sitemaps = await createAndUploadTranslatedSitemaps(categoryUrls, 'database_categories');
    allCategorySitemaps.allDatabaseCategoriesSitemaps = allCategorySitemaps.allDatabaseCategoriesSitemaps.concat(sitemaps);
  }

  {
    const sitemaps = await createAndUploadTranslatedSitemaps(subcategoryUrls, 'database_subcategories');
    allCategorySitemaps.allDatabaseCategoriesSitemaps = allCategorySitemaps.allDatabaseCategoriesSitemaps.concat(sitemaps);
  }

  {
    const sitemaps = await createAndUploadTranslatedSitemapsV2(profileUrls, 'database_profiles', true);
    allCategorySitemaps.allDatabaseProfilesSitemaps = allCategorySitemaps.allDatabaseProfilesSitemaps.concat(sitemaps);
  }

  // create sitemap index
  const sitemapIndexCreated = await createSitemapIndex(allCategorySitemaps, sitemapIndexName);
  const allSitemaps = [
    ...sitemapIndexCreated,
    ...Object.values(allCategorySitemaps).flat()
  ]
  await removeUnusedSitemaps(allSitemaps);
  if (process.env.NODE_ENV == 'prod') {
    await createRobotsTxt(sitemapIndexCreated)
  }
  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
  }catch(err){
    console.log(`createSitemap - Main function error handler: error is ==`, err);
  }
}

async function updateAutoIndexNowSitemap(req, res, next) {
  // run on prod worker only
  if (process.env.NODE_ENV !== 'prod') {
    return res.json({});
  }

  try{
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const urls = await getSitemapAutoIndexUrl()
  const BATCH_SIZE = 10000; // Maximum URLs per request
  if (urls.length === 0) {
    return res.json({})
  }

  for (let i = 0; i < urls.length; i += BATCH_SIZE) {
      const batch = urls.slice(i, i + BATCH_SIZE);
      try {
        const response = await axios.post(AUTO_INDEX_SEARCH_ENGINE_ENDPOINT, {
            host: 'boo.world',
            key: AUTO_INDEX_API_KEY,
            keyLocation: `https://boo.world/${AUTO_INDEX_API_KEY}.txt`,
            urlList: batch
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        console.log(`updateAutoIndexNowSitemap - Success: Submitted ${batch.length} URLs for IndexNow - With Status: ${response.status}`);
      } catch (error) {
        console.error(`updateAutoIndexNowSitemap - Error submitting batch of URLs for IndexNow:`, error.response?.data || error.message);
      }
  }
  console.log(`updateAutoIndexNowSitemap - bulk URL upload to IndexNow completed`);

  if (process.env.TESTING) {
    res.json({});
  }
  }catch(err){
    console.log(`updateAutoIndexNowSitemap - Main function error handler: error is ==`, err);
  }
}

module.exports = {
  updateQuestionScores,
  updateCommentScores,
  updateSitemap,
  updateAutoIndexNowSitemap,
  backfillInterestPoints,
};
